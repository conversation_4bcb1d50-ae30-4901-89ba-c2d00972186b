<!-- eslint-disable curly -->
<!-- eslint-disable antfu/top-level-function -->
<script setup lang="ts">
import {
  Calendar as IconCalendar,
  Copy as IconCopy,
  Save,
} from 'lucide-vue-next'

import type { Dayjs, OpUnitType } from 'dayjs'
import dayjs from 'dayjs'
import { useBreakpoints } from '@vueuse/core'
import ProjectSchedule from './components/ProjectSchedule.vue'
import ScheduleInforForm from './components/ScheduleInforForm.vue'
import ScheduleModal from './components/ScheduleModal.vue'
import type { DuplicateScheduleParams, EmployeeShift, OutsourceShift, OutsourceShiftParams, ProjectScheduleItem, ProjectScheduleParams, ScheduleItem, ScheduleParams, ScheduleShiftParams, TargetInfo, UpdateScheduleParams, UpdateScheduleShiftParams } from '~@/api/company/schedule'
import { createNewScheduleApi, createOutsourceShiftApi, createScheduleShiftApi, deleteEmployeeShiftApi, deleteOutsourceShiftApi, deleteScheduleApi, duplicateAnEmployeeShiftApi, duplicateAnOutsourceShiftApi, updateOutsourceShiftApi, updateScheduleApi, updateScheduleShiftApi } from '~@/api/company/schedule'
import type { WorkshiftItem } from '~@/api/company/work-shift'
import { getWorkshiftApi } from '~@/api/company/work-shift'
import logger from '~@/utils/logger'
import { ResponseStatusEnum } from '~@/enums/response-status-enum'
import type { EmployeeItem } from '~@/api/employee/employee'
import type { CheckedType } from '~@/layouts/basic-layout/typing'
import { useSchedules } from '~@/features/schedules/composables/useSchedules'
import DownloadButton from '~@/components/common/DownloadButton.vue'
import CreateButton from '~@/components/common/CreateButton.vue'
import { useFileDownloader } from '~@/composables/useFileDownloaderPdfme'
import { getSchedules } from '~@/features/schedules/services/scheduleService'

const { t } = useI18n()
const { exportSchedulePdf } = useFileDownloader()
const isMobile = useBreakpoints({
  xl: 1200,
}).smaller('xl')
const lang = computed(() => t('locale'))

const message = useMessage()

// Refs để lưu trữ các phần tử cần theo dõi
const projectScheduleRef = ref<HTMLElement | null>(null)
const scheduleInforFormRef = ref<HTMLElement | null>(null)

// Data Variables
const workshiftOptions = ref<WorkshiftItem[]>([])
const employeeData = ref<EmployeeItem[]>([])

// State Variables
const titleDate = ref<Dayjs>(dayjs())
const mode = ref<string>('week')
const projectInfoComponentKey = ref<number>(0)
const projectScheduleKey = ref<number>(0)
const visibleDownload = ref<boolean>(false)
const switchValue = ref<boolean>(false)

const selectedShift = ref<EmployeeShift | undefined>()
const selectedOutsourceShift = ref<OutsourceShift | undefined>()
const selectedSchedule = ref<ScheduleItem>()
const selectedWorkingDate = ref<string>('')
const isAllEmployeeShiftVisible = ref<boolean>(false)
const isEmployeeDragging = ref<boolean>(false)
const searchValue = ref<string>('')

const sourceDate = ref<Dayjs | undefined>(undefined)
const targetDate = ref<Dayjs>()
const isCopyLoading = ref<boolean>(false)
const visible = reactive<Record<string, boolean>>({})

const isScheduleModalVisible = ref<boolean>(false)

const selectedProjectId = ref<string>('')
const isShiftInfoVisible = ref<boolean>(false)
const isOutsourceShiftInfoVisible = ref<boolean>(false)
const isScheduleInfoVisible = ref<boolean>(false)

const { projectScheduleData, projectOptions, fetchSchedules, duplicateSchedule } = useSchedules()

const projectScheduleIds = ref<string[]>([])

// Helper để tạo ngày trong tuần
const generateWeekDays = computed((() => {
  const days = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT']
  const startOfWeek = titleDate.value.startOf('week')
  return Array.from({ length: 7 }, (_, i) => ({
    date: startOfWeek.add(i, 'day').format('YYYY-MM-DD'),
    day: days[i],
  }))
}))

async function getProjectSchedule() {
  try {
    const modeValue = mode.value as OpUnitType
    const params: ProjectScheduleParams = {
      fromDate: titleDate.value.startOf(modeValue).format('YYYY-MM-DD'),
      toDate: titleDate.value.endOf(modeValue).format('YYYY-MM-DD'),
      pageNum: 1,
      pageSize: 1000,
    }
    if (searchValue.value) {
      params.searchKeyword = searchValue.value
    }
    await fetchSchedules(params)
  }
  catch (e) {
    logger.error(e)
  }
}

async function getWorkshift() {
  try {
    const { data, status, code } = await getWorkshiftApi()
    if (status === 200) {
      logger.log('data', data?.items)
      workshiftOptions.value = data?.items ?? []
    }
    else {
      logger.error(t(code))
    }
  }
  catch (e) {
    logger.error(e)
  }
}

// Sử dụng computed property để định dạng currentDate
const formattedCurrentDate = computed(() => {
  if (lang.value === 'en')
    return titleDate.value.locale(lang.value).format('dddd, D MMMM YYYY')
  return titleDate.value.format('ddd, YYYY年MM月DD日')
})

const onPreviousDay = async () => {
  titleDate.value = titleDate.value.subtract(1, 'week')
  await getProjectSchedule()
}

const onNextDay = async () => {
  titleDate.value = titleDate.value.add(1, 'week')
  await getProjectSchedule()
}

const changeMode = async () => {
  logger.log('change mode: ', mode.value)
  await getProjectSchedule()
}

const messageNotify = useMessage()
const regex = /^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\d|3[01])$/

const isFormatValid = (date: string) => {
  if (!regex.test(date)) {
    messageNotify.error('Formatted date error')
    return false
  }
  return true
}

const getProjectScheduleIdsByDate = (date: string) => {
  projectScheduleData.value?.forEach((item: ProjectScheduleItem) => {
    item.shifts?.forEach((shift: EmployeeShift) => {
      if (shift.workingDate === date && shift.projectScheduleId) {
        projectScheduleIds.value.push(shift.projectScheduleId)
      }
    })
    item.outsourceShifts?.forEach((outsourceShift: OutsourceShift) => {
      if (outsourceShift.workingDate === date && outsourceShift.projectScheduleId) {
        projectScheduleIds.value.push(outsourceShift.projectScheduleId)
      }
    })
  })
}

const showPopover = (day: string) => {
  if (!isFormatValid(day))
    return
  const currentDate = dayjs(day, 'YYYY-MM-DD')
  const nextDate = dayjs(day, 'YYYY-MM-DD').add(1, 'day')
  sourceDate.value = currentDate
  targetDate.value = nextDate
  getProjectScheduleIdsByDate(day)
}

// async function duplicateSchedule(params: DuplicateScheduleParams) {
//   if (isCopyLoading.value)
//     return

//   isCopyLoading.value = true
//   try {
//     const { status, code } = await duplicateScheduleApi(params)
//     if (status === ResponseStatusEnum.SUCCESS) {
//       messageNotify.success(t(code))
//       await getProjectSchedule()
//     }
//     else {
//       messageNotify.error(t(code))
//     }
//   }
//   catch (e) {
//     console.error(e)
//   }
//   finally {
//     isCopyLoading.value = false
//   }
// }

const duplicateScheduleParams = ref<DuplicateScheduleParams>({
  projectScheduleIds: [],
  targetDateFrom: '',
  targetDateTo: '',
})

const handlePasteSchedule = async () => {
  duplicateScheduleParams.value.targetDateFrom = targetDate.value?.format('YYYY-MM-DD') ?? ''
  duplicateScheduleParams.value.targetDateTo = targetDate.value?.format('YYYY-MM-DD') ?? ''
  duplicateScheduleParams.value.projectScheduleIds = projectScheduleIds.value
  await duplicateSchedule(duplicateScheduleParams.value)
  Object.keys(visible).forEach((key) => {
    visible[key] = false
  })
  await getProjectSchedule()
}

interface DownloadScheduleFormstate {
  dateRange: [Dayjs, Dayjs]
}

const downloadScheduleFormstate = reactive<DownloadScheduleFormstate>({
  dateRange: [dayjs(), dayjs()],
})
const handleDownloadClick = async () => {
  console.log('handleDownloadClick')
  const params: ProjectScheduleParams = {
    fromDate: downloadScheduleFormstate.dateRange[0].format('YYYY-MM-DD'),
    toDate: downloadScheduleFormstate.dateRange[1].format('YYYY-MM-DD'),
  }

  visibleDownload.value = false

  const scheduleData: ProjectScheduleItem[] | undefined = await getSchedules(params)
  if (!scheduleData)
    return
  exportSchedulePdf(scheduleData)
}

const openDownloadModel = () => {
  visibleDownload.value = true
}

const openScheduleModal = () => {
  isScheduleModalVisible.value = true
}

// interface Params {
//   pageNum?: number
//   pageSize?: number
//   [key: string]: any
// }

const showShiftInfo = (shift: EmployeeShift, projectId: string) => {
  isShiftInfoVisible.value = true
  isScheduleInfoVisible.value = false
  isOutsourceShiftInfoVisible.value = false
  selectedProjectId.value = projectId
  selectedShift.value = shift
  selectedOutsourceShift.value = undefined
  selectedSchedule.value = undefined
  selectedWorkingDate.value = shift.workingDate
}
const showOutsourceShiftInfo = (outsourceShift: OutsourceShift, projectId: string) => {
  isOutsourceShiftInfoVisible.value = true
  isShiftInfoVisible.value = false
  isScheduleInfoVisible.value = false
  selectedProjectId.value = projectId
  selectedOutsourceShift.value = outsourceShift
  selectedShift.value = undefined
  selectedSchedule.value = undefined
  selectedWorkingDate.value = outsourceShift.workingDate
}

const showScheduleInfo = (projectId: string, workingDate: string, _: 'EDIT' | 'ADD_NEW', schedule?: ScheduleItem) => {
  isScheduleInfoVisible.value = true
  isShiftInfoVisible.value = false
  isOutsourceShiftInfoVisible.value = false
  selectedSchedule.value = schedule
  selectedWorkingDate.value = workingDate
  selectedProjectId.value = projectId
}

const handleClickOutScheduleInfoForm = () => {
  isScheduleInfoVisible.value = false
  isShiftInfoVisible.value = false
  isOutsourceShiftInfoVisible.value = false
  selectedSchedule.value = undefined
  selectedShift.value = undefined
  selectedOutsourceShift.value = undefined
}

const createSchedule = async (params: ScheduleParams) => {
  const { data, status, code } = await createNewScheduleApi(params)
  if (status === ResponseStatusEnum.SUCCESS) {
    message.success(t(code))
    selectedSchedule.value = data ?? undefined
    await getProjectSchedule()
    if (!selectedSchedule.value?.shifts || selectedSchedule.value?.shifts.length === 0)
      return
    showShiftInfo(selectedSchedule.value?.shifts[0], selectedSchedule.value?.projectId ?? '')
    isScheduleModalVisible.value = false
  }
  else {
    message.error(t(code))
  }
}

const updateSchedule = async (scheduleId: string, params: UpdateScheduleParams) => {
  const { data, status, code } = await updateScheduleApi(scheduleId, params)
  if (status === ResponseStatusEnum.SUCCESS) {
    logger.log('data', data)
    message.success(t(code))
    selectedSchedule.value = data ?? undefined
    await getProjectSchedule()
  }
  else {
    message.error(t(code))
  }
}

const updateProjectScheduleByShift = (shift: EmployeeShift, projectId: string) => {
  const project = projectScheduleData.value?.find((item: ProjectScheduleItem) => item.projectId === projectId)
  if (!project)
    return

  let employeeShift = project.shifts?.find((item: EmployeeShift) => item.employeeShiftId === shift.employeeShiftId)
  if (!employeeShift)
    return

  employeeShift = {
    ...shift,
  }
}

const updateProjectScheduleByOutsourceShift = (shift: OutsourceShift, projectId: string) => {
  const idx1 = projectScheduleData.value?.findIndex((item: ProjectScheduleItem) => item.projectId === projectId)
  if (idx1 === -1 || !projectScheduleData.value || !idx1)
    return

  const projectSchedule = projectScheduleData.value[idx1]

  const idx2 = projectSchedule.outsourceShifts.findIndex((item: OutsourceShift) => item.outSourceShiftId === shift.outSourceShiftId)
  if (idx2 === -1 || !projectScheduleData.value || !idx2)
    return

  projectScheduleData.value[idx1].outsourceShifts[idx2] = shift
}

const updateEmployeeShift = async (employeeShiftId: string, projectId: string, params: UpdateScheduleShiftParams) => {
  const { data, status, code } = await updateScheduleShiftApi(employeeShiftId, params)
  if (status === ResponseStatusEnum.SUCCESS) {
    selectedShift.value = data ?? undefined
    message.success(t(code))
    updateProjectScheduleByShift(data as EmployeeShift, projectId)
    await getProjectSchedule()
  }
  else {
    message.error(t(code))
  }
}

const updateOutsourceShift = async (outsourceShiftId: string, projectId: string, params: UpdateScheduleShiftParams) => {
  const { data, status, code } = await updateOutsourceShiftApi(outsourceShiftId, params)
  if (status === ResponseStatusEnum.SUCCESS) {
    selectedOutsourceShift.value = data ?? undefined
    message.success(t(code))
    updateProjectScheduleByOutsourceShift(data as OutsourceShift, projectId)
    await getProjectSchedule()
  }
  else {
    message.error(t(code))
  }
}

const showAllEmployeeShift = () => {
  switchValue.value = true
  isAllEmployeeShiftVisible.value = true
}

function refreshProjectSchedule(projectId: string, shift: EmployeeShift) {
  // const projectIdx = projectScheduleData.value?.findIndex((item: ProjectScheduleItem) => item.projectId === projectId)
  // if (projectIdx === -1 || !projectScheduleData.value || !projectIdx)
  //   return

  // projectScheduleData.value[projectIdx].shifts.push(shift)

  const shifts = projectScheduleData.value?.find((item: ProjectScheduleItem) => item.projectId === projectId)?.shifts
  if (!shifts)
    return
  shifts.push(shift)
}

function refreshOutsourceShift(projectId: string, outsourceShift: OutsourceShift) {
  // const projectIdx = projectScheduleData.value?.findIndex((item: ProjectScheduleItem) => item.projectId === projectId)
  // if (projectIdx === -1 || !projectScheduleData.value || !projectIdx)
  //   return

  // projectScheduleData.value[projectIdx].outsourceShifts.push(outsourceShift)

  const outsourceShifts = projectScheduleData.value?.find((item: ProjectScheduleItem) => item.projectId === projectId)?.outsourceShifts
  if (!outsourceShifts)
    return
  outsourceShifts.push(outsourceShift)
}

const createEmployeeShift = async (projectId: string, scheduleId: string, params: ScheduleShiftParams) => {
  const { data, status, code } = await createScheduleShiftApi(scheduleId, params)
  if (status === ResponseStatusEnum.SUCCESS) {
    showShiftInfo(data as EmployeeShift, projectId)
    refreshProjectSchedule(projectId, data as EmployeeShift)
  }
  else {
    message.error(t(code))
  }
}

const createOutsourceShift = async (projectId: string, scheduleId: string, params: OutsourceShiftParams) => {
  const { data, status, code } = await createOutsourceShiftApi(scheduleId, params)
  if (status === ResponseStatusEnum.SUCCESS) {
    showOutsourceShiftInfo(data as OutsourceShift, projectId)
    refreshOutsourceShift(projectId, data as OutsourceShift)
  }
  else {
    message.error(t(code))
  }
}

const deleteSchedule = async (scheduleId: string) => {
  try {
    const { status, code } = await deleteScheduleApi(scheduleId)
    if (status === ResponseStatusEnum.SUCCESS) {
      message.success(t(code))
      await getProjectSchedule()
    }
    else {
      message.error(t(code))
      throw new Error('Error: deleteSchedule')
    }
  }
  catch (e) {
  }
}

const deleteEmployeeShift = async (employeeShiftId: string) => {
  try {
    const { status, code } = await deleteEmployeeShiftApi(employeeShiftId)
    if (status === ResponseStatusEnum.SUCCESS) {
      message.success(t(code))
      await getProjectSchedule()
    }
    else {
      message.error(t(code))
    }
  }
  catch (e) {
  }
}

const deleteOutsourceShift = async (outsourceShiftId: string) => {
  try {
    const { status, code } = await deleteOutsourceShiftApi(outsourceShiftId)
    if (status === ResponseStatusEnum.SUCCESS) {
      message.success(t(code))
      await getProjectSchedule()
    }
    else {
      message.error(t(code))
    }
  }
  catch (e) {
  }
}

const updateIsEmployeeDragging = (isDragging: boolean) => {
  isEmployeeDragging.value = isDragging
}

const onSwitchChange = (checked: CheckedType, _: Event) => {
  switchValue.value = checked === 'true' || checked === true
  if (switchValue.value) {
    isAllEmployeeShiftVisible.value = true
  }
  else {
    isAllEmployeeShiftVisible.value = false
  }
}

const duplicateAnEmployeeShift = async (copiedEmployeeShiftId: string, targetInfo: TargetInfo) => {
  const { data, status, code } = await duplicateAnEmployeeShiftApi(copiedEmployeeShiftId, targetInfo)
  if (status === ResponseStatusEnum.SUCCESS) {
    refreshProjectSchedule(targetInfo.projectId!, data as EmployeeShift)
    message.success(t(code))
  }
  else {
    message.error(t(code))
  }
}

const duplicateAnOutsourceShift = async (copiedOutsourceShiftId: string, targetInfo: TargetInfo) => {
  const { data, status, code } = await duplicateAnOutsourceShiftApi(copiedOutsourceShiftId, targetInfo)
  if (status === ResponseStatusEnum.SUCCESS) {
    refreshOutsourceShift(targetInfo.projectId!, data as OutsourceShift)
    message.success(t(code))
  }
  else {
    message.error(t(code))
  }
}

const onSearch = () => {
  getProjectSchedule()
}

onMounted(async () => {
  const promises = [getProjectSchedule(), getWorkshift()]
  Promise.all(promises)
})
</script>

<template>
  <page-container>
    <div class="flex flex-reverse gap-x-4">
      <div class="xl:flex-1 p-2 bg-white shadow-sm rounded-lg">
        <!-- Thanh điều hướng phía trên -->
        <div class="flex justify-between items-center mb-4">
          <a-input v-if="!isMobile" v-model:value="searchValue" type="text"
            class="border-1 border-gray-300 border-solid p-2 rounded w-[240px] h-[32px]" :placeholder="t('search')"
            @change="onSearch" @press-enter="onSearch">
            <template #prefix>
              <CarbonSearch />
            </template>
          </a-input>
          <div class="flex items-center">
            <CarbonArrowLeft class="cursor-pointer hover:brightness-125" @click="onPreviousDay" />
            <span class="text-black p-2 text-[20px] font-500">{{ formattedCurrentDate }}</span>
            <CarbonArrowRight class="cursor-pointer hover:brightness-125" @click="onNextDay" />
            <CarbonCalendar color="#74797A" class="ml-2" />
          </div>
          <div class="flex items-center gap-2">
            <a-radio-group v-model:value="mode" @change="changeMode">
              <!-- <a-radio-button value="day" disabled>
                {{ t('type.day') }}
              </a-radio-button> -->
              <!-- <a-radio-button value="week">
                {{ t('type.week') }}
              </a-radio-button> -->
              <!-- <a-radio-button value="month">
                {{ t('type.month') }}
              </a-radio-button> -->
            </a-radio-group>
            <!-- <a-button type="primary" class="flex items-center" @click="openScheduleModal">
              {{ t('button.create') }}
              <template #icon>
                <PlusOutlined />
              </template>
            </a-button> -->
            <!-- <a-button class="flex items-center" @click="openDownloadModel">
              {{ t('button.download') }}
              <template #icon>
                <DownloadOutlined />
              </template>
            </a-button> -->
            <CreateButton :text="t('button.create')" :on-click="openScheduleModal" />
            <DownloadButton :disabled="true" :text="t('button.download')" :on-click="openDownloadModel" />
          </div>
        </div>
        <!-- Thanh tiêu đề ngày trong tuần -->
        <div
          class="flex text-center border-l-1 border-r-1 border-t-1 border-b-0 border-gray-300 border-solid rounded-t bg-white gap-x-2 overflow-y-scroll sticky top-20">
          <div class="flex w-1/8 items-center p-2 border-r gap-x-2">
            <a-switch v-model:checked="switchValue" @change="onSwitchChange" />
            <p>{{ t('showAllEmployeeShifts') }}</p>
          </div>
          <div
            class="flex w-7/8 items-center justify-center border-l-1 border-r-0 border-t-0 border-b-0 border-gray-300 border-solid">
            <div v-for="weekDay in generateWeekDays" :key="weekDay.date"
              class="flex w-1/7 border-r-1 border-l-0 border-t-0 border-b-0 border-gray-300 border-solid items-center justify-center p-1 h-full">
              <div class="flex justify-between items-center gap-x-2">
                <span class="text-[20px]">{{ dayjs(weekDay.date).format('DD') }}</span>
                <span>{{ t(weekDay.day) }}</span>
                <a-popover v-model:visible="visible[weekDay.date]" trigger="click">
                  <template #content>
                    <div class="flex justify-between gap-8">
                      <div class="w-60">
                        <div class="flex items-center mb-4">
                          <IconCalendar class="mr-2" :size="20" />
                          <h2 class="text-md font-semibold">
                            {{ t('title.targetDate') }}
                          </h2>
                        </div>

                        <div class="space-y-4">
                          <div>
                            <a-date-picker v-model:value="targetDate" class="w-full px-3 py-2" />
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="mt-4 flex justify-center">
                      <button :loading="isCopyLoading"
                        class="flex items-center px-2 py-1 gap-x-2 bg-blue-500 rounded-lg hover:bg-blue-600 text-white"
                        @click="handlePasteSchedule">
                        <Save />
                        {{ t('button.save') }}
                      </button>
                    </div>
                  </template>
                  <div class="cursor-pointer" @click="showPopover(weekDay.date)">
                    <IconCopy class="text-blue-600" :size="15" />
                  </div>
                </a-popover>
              </div>
            </div>
          </div>
        </div>
        <!-- Lịch phân công (grid) -->
        <div :key="projectInfoComponentKey"
          class="flex flex-col gap-y-2 border-l-1 border-r-1 border-t-0 border-b-0 border-gray-300 border-solid rounded-b bg-[#DEF0FF] overflow-y-scroll h-[calc(100vh-25rem)] md:h-[calc(100vh-20rem)] xl:h-[calc(100vh-15rem)]">
          <ProjectSchedule ref="projectScheduleRef" :key="projectScheduleKey"
            :project-schedule-data="projectScheduleData" :title-date="titleDate"
            :is-show-all-employee-shift="isAllEmployeeShiftVisible" :is-employee-dragging="isEmployeeDragging"
            @show-shift-info="showShiftInfo" @show-outsource-shift-info="showOutsourceShiftInfo"
            @show-schedule-info="showScheduleInfo" @create-employee-shift="createEmployeeShift"
            @create-outsource-shift="createOutsourceShift" @duplicate-an-employee-shift="duplicateAnEmployeeShift"
            @duplicate-an-outsource-shift="duplicateAnOutsourceShift" @delete-schedule="deleteSchedule"
            @delete-employee-shift="deleteEmployeeShift" @delete-outsource-shift="deleteOutsourceShift"
            @create-schedule="createSchedule" />
        </div>
      </div>
      <ScheduleInforForm ref="scheduleInforFormRef" :employee-data="employeeData" :selected-shift="selectedShift"
        :selected-outsource-shift="selectedOutsourceShift" :selected-schedule="selectedSchedule"
        :is-shift-info-visible="isShiftInfoVisible" :is-outsource-shift-info-visible="isOutsourceShiftInfoVisible"
        :is-schedule-info-visible="isScheduleInfoVisible" :selected-working-date="selectedWorkingDate"
        :project-id="selectedProjectId" @handle-click-out-schedule-info-form="handleClickOutScheduleInfoForm"
        @create-schedule="createSchedule" @update-schedule="updateSchedule" @update-employee-shift="updateEmployeeShift"
        @update-outsource-shift="updateOutsourceShift" @show-all-employee-shift="showAllEmployeeShift"
        @update-is-employee-dragging="updateIsEmployeeDragging" @delete-employee-shift="deleteEmployeeShift"
        @delete-outsource-shift="deleteOutsourceShift" @delete-schedule="deleteSchedule" />
    </div>
    <a-modal v-model:open="visibleDownload" :title="t('title.download-schedule')" :width="400"
      @ok="handleDownloadClick">
      <a-form layout="vertical">
        <a-form-item :label="t('timeRange')" name="workshiftId"
          :rules="[{ required: false, message: t('form.required') }]">
          <a-range-picker v-model:value="downloadScheduleFormstate.dateRange" />
        </a-form-item>
      </a-form>
    </a-modal>

    <a-drawer v-model:open="isScheduleModalVisible" :title="t('title.createSchedule')" placement="right" width="40%">
      <ScheduleModal :project-options="projectOptions" :schedule="selectedSchedule" @create-schedule="createSchedule" />
    </a-drawer>
  </page-container>
</template>

<style scoped></style>
